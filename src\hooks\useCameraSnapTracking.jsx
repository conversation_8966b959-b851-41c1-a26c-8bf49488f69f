'use client';
import { useRef, useCallback } from 'react';
import * as THREE from 'three';

/**
 * Custom hook for tracking camera snap operations and object changes
 * Provides smooth camera transitions and object position/rotation tracking
 */
export function useCameraSnapTracking() {
  // Snap operation state
  const snapInProgress = useRef(false);
  const targetCameraPos = useRef(new THREE.Vector3());
  const targetLookAtPos = useRef(new THREE.Vector3());
  const currentCameraPos = useRef(new THREE.Vector3());
  const currentLookAtPos = useRef(new THREE.Vector3());
  const snapProgress = useRef(0);
  
  // Object tracking state
  const trackedObject = useRef(null);
  const lastObjectPosition = useRef(new THREE.Vector3());
  const lastObjectQuaternion = useRef(new THREE.Quaternion());
  
  // Configuration
  const snapDuration = 1.0; // Duration in seconds
  const objectPositionThreshold = 0.01; // Minimum movement to trigger update
  const objectRotationThreshold = 0.01; // Minimum rotation to trigger update

  /**
   * Check if tracked object has moved or rotated significantly
   */
  const hasObjectChanged = useCallback((obj) => {
    if (!obj) return false;
    
    const currentPos = new THREE.Vector3();
    obj.getWorldPosition(currentPos);
    const currentQuat = new THREE.Quaternion();
    obj.getWorldQuaternion(currentQuat);
    
    const positionChanged = currentPos.distanceTo(lastObjectPosition.current) > objectPositionThreshold;
    const rotationChanged = Math.abs(currentQuat.angleTo(lastObjectQuaternion.current)) > objectRotationThreshold;
    
    return positionChanged || rotationChanged;
  }, []);

  /**
   * Calculate camera position based on object position and orientation
   */
  const calculateCameraPosition = useCallback((targetObject, offset = new THREE.Vector3(0, 1, 2)) => {
    const objectWorldPosition = new THREE.Vector3();
    targetObject.getWorldPosition(objectWorldPosition);

    // Apply object's rotation to the offset
    const rotatedOffset = offset.clone();
    rotatedOffset.applyQuaternion(targetObject.quaternion);
    
    return new THREE.Vector3().addVectors(objectWorldPosition, rotatedOffset);
  }, []);

  /**
   * Initialize snap operation to target object
   */
  const initializeSnap = useCallback((targetObject, camera, controls, offset) => {
    if (!targetObject || !camera || !controls) {
      console.warn("initializeSnap: Missing required parameters");
      return false;
    }

    // Store reference to tracked object
    trackedObject.current = targetObject;

    // Calculate target positions
    const objectWorldPosition = new THREE.Vector3();
    targetObject.getWorldPosition(objectWorldPosition);
    
    const calculatedCameraPosition = calculateCameraPosition(targetObject, offset);

    // Store current positions for smooth interpolation
    currentCameraPos.current.copy(camera.position);
    currentLookAtPos.current.copy(controls.target);

    // Store target positions
    targetCameraPos.current.copy(calculatedCameraPosition);
    targetLookAtPos.current.copy(objectWorldPosition);

    // Store last known object state for tracking
    lastObjectPosition.current.copy(objectWorldPosition);
    lastObjectQuaternion.current.copy(targetObject.quaternion);

    // Initialize snap animation
    snapProgress.current = 0;
    snapInProgress.current = true;

    console.log("Snap initialized to:", targetObject.name, "from:", camera.position.toArray(), "to:", calculatedCameraPosition.toArray());
    return true;
  }, [calculateCameraPosition]);

  /**
   * Update snap animation progress
   */
  const updateSnapAnimation = useCallback((delta, camera, controls) => {
    if (!snapInProgress.current || !camera || !controls) return false;

    snapProgress.current += delta / snapDuration;
    
    if (snapProgress.current >= 1.0) {
      // Snap complete
      snapProgress.current = 1.0;
      snapInProgress.current = false;
      console.log("Snap animation completed");
      return true; // Snap completed
    }
    
    // Smooth interpolation using easing function (ease-out cubic)
    const easeProgress = 1 - Math.pow(1 - snapProgress.current, 3);
    
    // Interpolate camera position
    const newCameraPos = new THREE.Vector3().lerpVectors(
      currentCameraPos.current,
      targetCameraPos.current,
      easeProgress
    );
    
    // Interpolate look-at target
    const newLookAtPos = new THREE.Vector3().lerpVectors(
      currentLookAtPos.current,
      targetLookAtPos.current,
      easeProgress
    );
    
    // Apply positions
    camera.position.copy(newCameraPos);
    controls.target.copy(newLookAtPos);
    controls.update();
    
    return false; // Snap still in progress
  }, []);

  /**
   * Clear tracking state
   */
  const clearTracking = useCallback(() => {
    trackedObject.current = null;
    snapInProgress.current = false;
    snapProgress.current = 0;
  }, []);

  /**
   * Get current tracking state
   */
  const getTrackingState = useCallback(() => ({
    isSnapping: snapInProgress.current,
    progress: snapProgress.current,
    trackedObject: trackedObject.current,
    hasTrackedObject: !!trackedObject.current
  }), []);

  return {
    // State getters
    isSnapping: () => snapInProgress.current,
    getTrackingState,
    
    // Object tracking
    hasObjectChanged,
    getTrackedObject: () => trackedObject.current,
    
    // Snap operations
    initializeSnap,
    updateSnapAnimation,
    clearTracking,
    
    // Utilities
    calculateCameraPosition
  };
}
