'use client'

import TextWrapper from '@/components/TextWrapper'
import { settings } from '@/libs/siteSettings'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { BsHeartFill } from 'react-icons/bs'
import { IoMdHeart } from 'react-icons/io'
import { IoShareOutline, IoChevronBackSharp } from "react-icons/io5";
import Image from 'next/image'

export default function BuildPageComponent({children,data}) {
    const iconCss='text-4xl md:text-5xl p-2 cursor-pointer text-gray-50 rounded-full p-1 cursor-pointer'
    const imageHeightArray=['h-[100svh]','h-[65svh]','h-[45svh]','h-[0svh]']
    const textHeightArray=['h-[4svh]','h-[35svh]','h-[55svh]','h-[88svh]']
    const [heightIndex,setHeightIndex]=useState(0)
    const [sectionIndex,setSectionIndex]=useState(0)
    const router=useRouter()

    const {renders,drawings,_360sImages}=data
    const scetionBtnArray=[renders,drawings,_360sImages]    

    const handleShare = async () => {
      try {
        // Get the building name from the pathname or data
        const buildingName = data?.buildingTitle || 'this building';

        // Check if the Web Share API is available
        if (navigator.share) {
          // Share the URL using the Web Share API
          await navigator.share({
            title: `Check out ${buildingName} at ${settings.siteName.name}.com!`,
            text: 'I found this amazing building design. Take a look!',
            url: window.location.href
          });
          console.log('Content shared successfully');
        } else {
          // Fallback for browsers that don't support Web Share API
          await navigator.clipboard.writeText(window.location.href);
          alert('URL copied to clipboard!');
        }
      } catch (error) {
        console.error('Error sharing:', error);

        // If direct sharing fails, try to copy to clipboard as a last resort
        try {
          await navigator.clipboard.writeText(window.location.href);
          alert('URL copied to clipboard!');
        } catch (clipboardError) {
          console.error('Clipboard copy failed:', clipboardError);
          alert('Could not share. Please copy the URL manually.');
        }
      }
    }

    const handleLike = () => {
      // Function body left empty as requested
    }

    const handleBack = () => {
      // Function body left empty as requested
      router.back()
    }

    // console.log('BuildPageComponent:',scetionBtnArray)
  return (
    <div className='flex relative flex-col md:flex-row w-full h-full'>
      <div
        className={`imagesWrapper w-full duration-300 ease-linear ${imageHeightArray[heightIndex]} relative overflow-hidden`}
        // style={{transform:`translateY(-${heightIndex*100}%)`}}
      >
        <div 
          style={{transform:`translateX(-${sectionIndex*100}%)`}}
          className='flex h-full duration-1000 ease-linear'
        >
          {children}
        </div>
        <div className='flex items-center justify-center w-fit h-fit absolute left-2 top-20'>
          <IoChevronBackSharp onClick={() => handleBack()} className={iconCss}/>
        </div>
        <div className='flex items-center justify-center w-fit h-fit absolute right-2 top-20'>
          <IoShareOutline onClick={() => handleShare()} className={iconCss}/>
          <IoMdHeart onClick={() => handleLike()} className={iconCss}/>
        </div>
        <div className={`${heightIndex > 0 ? 'bottom-2' : 'bottom-9'} flex items-center justify-center w-fit h-fit absolute right-0 gap-4 left-0 mx-auto md:bottom-2 md:right-2`}>
          {[...children].map((i,index)=>
            <div 
              onClick={()=>{setSectionIndex(index)}}
              key={index} 
              className='flex shadow relative min-h-14 md:min-h-20 cursor-pointer items-center justify-center min-w-28 md:min-w-52 md:rounded-2xl rounded-xl border-[6px] border-white overflow-hidden hover:scale-105 duration-300 ease-linear'
            >
              <Image src={scetionBtnArray?.[index]?.sort((a,b) => a.name.localeCompare(b.name))?.[0]?.url} className='object-cover brightness-95 hover:brightness-110 w-full h-full' alt="view" fill/>
            </div>
          )}
        </div>
      </div>

      <div
        className={`textWrapper w-full md:w-1/3 md:h-full ${textHeightArray[heightIndex]} md:rounded-t-none md:bg-transparent overflow-hidden absolute md:relative bottom-0 duration-300 rounded-t-2xl bg-gray-50 ease-linear`}
      >
        <div onClick={() => {setHeightIndex(heightIndex===imageHeightArray.length-1 ? 0 : heightIndex+1)}} className='flex md:hidden w-full h-6 items-center justify-center bg-gray-50 rounded-t-2xl overflow-hidden bottom-4'>
          <hr className='border-3 border-gray-500 w-14 rounded-full m-auto'/>
        </div>
        <div className={`flex flex-col gap-2 relative w-full h-full text-wrap overflow-y-auto`}>
          <TextWrapper data={data}/>
        </div>
      </div>
    </div>
  )
}
