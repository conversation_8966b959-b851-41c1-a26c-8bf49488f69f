'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
import ExperienceAR from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceControls from './ExperienceControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import CameraControlsDebugger from './CameraControlsDebugger'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  // console.log('ExperienceWorld:',experienceState)
  return (
    <>
      <Canvas>
        <Suspense fallback={null}>
          {experienceState?.modeAR
            ? <ExperienceAR data={data}/>
            : <>
                <CameraControlsErrorBoundary>
                  <ExperienceControls data={data} />
                </CameraControlsErrorBoundary>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                <CameraControlsDebugger />
              </>
          }
        </Suspense>
      </Canvas>
    </>
  )
}