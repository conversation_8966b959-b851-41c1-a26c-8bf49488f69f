'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree, useFrame } from '@react-three/fiber'; // Keep useFrame
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import * as THREE from 'three';

export default function ExperienceControls({ data }) {
  const { experienceState, experienceDispatch } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true); // Renamed for clarity
  const refControls = useRef(null);
  const { camera, scene } = useThree();

  // We'll use a ref to manage the state of the snap operation
  const snapInProgress = useRef(false);
  const targetCameraPos = useRef(new THREE.Vector3());
  const targetLookAtPos = useRef(new THREE.Vector3());

  /**
   * Snaps the camera instantly to a target object's position and makes it look at it.
   * This function sets up the target values for the useFrame loop.
   */
  const instantSnapToTarget = useCallback((targetObject) => {
    if (!refControls.current || !targetObject) {
      console.warn("instantSnapToTarget called but controls or targetObject is null.");
      return;
    }

    // Calculate the target camera position and look-at point
    const objectWorldPosition = new THREE.Vector3();
    targetObject.getWorldPosition(objectWorldPosition);

    const cameraOffset = new THREE.Vector3(0, 0, 0.5); // Adjust this as needed
    cameraOffset.applyQuaternion(targetObject.quaternion);
    const calculatedCameraPosition = new THREE.Vector3().addVectors(objectWorldPosition, cameraOffset);

    // Store the target positions in refs
    targetCameraPos.current.copy(calculatedCameraPosition);
    targetLookAtPos.current.copy(objectWorldPosition);

    // Disable controls and flag snap as in progress
    setControlsEnabled(false);
    snapInProgress.current = true; // Signal that a snap is active

    console.log("Instant snap initiated to:", targetObject.name, "target cam:", calculatedCameraPosition.toArray());

  }, []); // Dependencies: camera is used to clone, but we only set target, not clone

  /**
   * Effect to handle activeRoomSnap changes and trigger the instant snap.
   */
  useEffect(() => {
    if (experienceState?.activeRoomSnap && scene) {
      const foundObject = scene.getObjectByName(experienceState.activeRoomSnap);
      if (foundObject) {
        instantSnapToTarget(foundObject);
      } else {
        console.warn(`Object with name "${experienceState.activeRoomSnap}" not found in the scene.`);
      }
    }
  }, [scene, experienceState?.activeRoomSnap, instantSnapToTarget]);


  /**
   * useFrame hook to handle the camera update and controls re-enabling.
   * This ensures the update() call happens on the next frame after setting positions.
   */
  useFrame(() => {
    const controls = refControls.current;
    if (snapInProgress.current && controls) {
      // Direct assignment here because it's an "instant" snap
      camera.position.copy(targetCameraPos.current);
      controls.target.copy(targetLookAtPos.current);

      // Crucially, force update the controls immediately after setting
      controls.update();

      // Only re-enable controls AFTER the update has happened on this frame
      snapInProgress.current = false; // Mark snap as complete
      setControlsEnabled(true);
      console.log("Controls re-enabled after snap.");
    }
  });


  // Function to cycle through snap points (if you still need this for internal testing/UI)
  const cycleSnapPoints = useCallback(() => {
    if (!data?.snapPoints || data.snapPoints.length === 0) {
      console.warn("No snap points defined in 'data'.");
      return;
    }

    const currentIndex = data.snapPoints.findIndex(
      (snapName) => snapName === experienceState?.activeRoomSnap
    );

    const nextIndex = (currentIndex + 1) % data.snapPoints.length;
    const nextSnapName = data.snapPoints[nextIndex];

    experienceDispatch({ type: 'SET_ACTIVE_ROOM_SNAP', payload: nextSnapName });
  }, [data, experienceState?.activeRoomSnap, experienceDispatch]);

  return (
    <OrbitControls
      ref={refControls}
      minDistance={experienceState?.firstPersonView ? 0.0 : data?.minDistance}
      maxDistance={experienceState?.firstPersonView ? 0.5 : data?.maxDistance}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
      rotateSpeed={-0.25}
      enabled={controlsEnabled} // Use controlsEnabled state
      enablePan={false}
      enableDamping={true} // Still recommended for smooth user control
      dampingFactor={0.05}
    />
  );
}