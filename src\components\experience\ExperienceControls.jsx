'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import * as THREE from 'three';

export default function ExperienceControls({ data }) {
  const { experienceState } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const [snapObject, setSnapObject] = useState(null);
  const [isSnapping, setIsSnapping] = useState(false);
  const refControls = useRef(null);
  const { camera, scene } = useThree();

  // Early return if essential dependencies are missing
  if (!camera || !scene) {
    console.warn("ExperienceControls: Missing camera or scene");
    return null;
  }

  /**
   * First-Person Camera Snap Function
   * Snaps camera to exact position and rotation of target object
   */
  const snapCameraToFirstPerson = useCallback((targetObject) => {
    if (!targetObject || !camera || !refControls.current) {
      console.warn("snapCameraToFirstPerson: Missing required parameters");
      return false;
    }

    try {
      console.log(`Starting first-person snap to object: ${targetObject.name}`);

      // Disable controls immediately
      setControlsEnabled(false);
      setIsSnapping(true);

      // Get target object's world position and rotation
      const targetPosition = new THREE.Vector3();
      const targetQuaternion = new THREE.Quaternion();
      const targetScale = new THREE.Vector3();

      targetObject.matrixWorld.decompose(targetPosition, targetQuaternion, targetScale);

      // Set camera to exact position of the snap object
      camera.position.copy(targetPosition);

      // Apply the same rotation as the snap object
      camera.quaternion.copy(targetQuaternion);

      // Calculate a forward-looking target point based on object's rotation
      const forwardDirection = new THREE.Vector3(0, 0, -1); // Default forward direction
      forwardDirection.applyQuaternion(targetQuaternion);
      const lookAtTarget = targetPosition.clone().add(forwardDirection.multiplyScalar(5));

      // Update OrbitControls target
      refControls.current.target.copy(lookAtTarget);
      refControls.current.update();

      console.log('First-person snap completed:', {
        position: targetPosition.toArray(),
        rotation: targetQuaternion.toArray(),
        lookAtTarget: lookAtTarget.toArray()
      });

      // Re-enable controls after 3 seconds
      setTimeout(() => {
        setControlsEnabled(true);
        setIsSnapping(false);
        console.log('Controls re-enabled after first-person snap');
      }, 3000);

      return true;
    } catch (error) {
      console.error('Error in snapCameraToFirstPerson:', error);
      setControlsEnabled(true);
      setIsSnapping(false);
      return false;
    }
  }, [camera]);

  /**
   * Camera Reset Function
   * Resets camera to specified location with smooth animation
   * Re-enables first-person view mode with new min/max distances
   */
  const resetCameraToLocation = useCallback((targetPosition, targetLookAt) => {
    if (!camera || !refControls.current) {
      console.warn("resetCameraToLocation: Missing required parameters");
      return false;
    }

    try {
      console.log('Starting camera reset to location:', {
        position: targetPosition?.toArray?.() || targetPosition,
        lookAt: targetLookAt?.toArray?.() || targetLookAt
      });

      // Store initial positions for lerp animation
      const startPosition = camera.position.clone();
      const startTarget = refControls.current.target.clone();

      const endPosition = targetPosition instanceof THREE.Vector3 ?
        targetPosition : new THREE.Vector3(...targetPosition);
      const endTarget = targetLookAt instanceof THREE.Vector3 ?
        targetLookAt : new THREE.Vector3(...targetLookAt);

      // Disable controls during animation
      setControlsEnabled(false);
      setIsSnapping(true);

      // Animation parameters
      let animationProgress = 0;
      const animationDuration = 2000; // 2 seconds
      const startTime = Date.now();

      const animateReset = () => {
        const currentTime = Date.now();
        animationProgress = Math.min((currentTime - startTime) / animationDuration, 1);

        // Use smooth easing function (ease-in-out)
        const easeProgress = animationProgress < 0.5
          ? 2 * animationProgress * animationProgress
          : 1 - Math.pow(-2 * animationProgress + 2, 3) / 2;

        // Lerp camera position
        camera.position.lerpVectors(startPosition, endPosition, easeProgress);

        // Lerp controls target
        refControls.current.target.lerpVectors(startTarget, endTarget, easeProgress);
        refControls.current.update();

        if (animationProgress < 1) {
          requestAnimationFrame(animateReset);
        } else {
          // Animation complete
          console.log('Camera reset animation completed');

          // Re-enable first-person view mode
          // Note: This would require experienceDispatch if we want to update the state
          // For now, just re-enable controls
          setControlsEnabled(true);
          setIsSnapping(false);
        }
      };

      // Start animation
      requestAnimationFrame(animateReset);
      return true;

    } catch (error) {
      console.error('Error in resetCameraToLocation:', error);
      setControlsEnabled(true);
      setIsSnapping(false);
      return false;
    }
  }, [camera]);

  /**
   * Handle activeRoomSnap changes and trigger first-person camera snap
   */
  useEffect(() => {
    if (!scene) return;

    try {
      if (experienceState?.activeRoomSnap) {
        const snapObjectName = experienceState.activeRoomSnap.split('.')[0];
        console.log('Looking for snap object:', snapObjectName);

        const foundObject = scene.getObjectByName(snapObjectName);
        if (foundObject) {
          setSnapObject(foundObject);
          console.log('Found snap object:', foundObject);

          // Trigger first-person snap
          const snapSuccess = snapCameraToFirstPerson(foundObject);
          if (snapSuccess) {
            console.log('First-person snap initiated successfully');
          }
        } else {
          console.warn(`Snap object "${snapObjectName}" not found in scene`);
          setSnapObject(null);
        }
      } else {
        // Clear snap object when no active snap
        setSnapObject(null);
        if (isSnapping) {
          setIsSnapping(false);
          setControlsEnabled(true);
        }
      }
    } catch (error) {
      console.error('Error in activeRoomSnap effect:', error);
    }
  }, [scene, experienceState?.activeRoomSnap, snapCameraToFirstPerson, isSnapping]);

  console.log('ExperienceControls snap object:', snapObject);

  // Safe parsing of distance values from data
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 50);

  return (
    <OrbitControls
      ref={refControls}
      minDistance={minDistance}
      maxDistance={maxDistance}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
      rotateSpeed={-0.25}
      enabled={controlsEnabled}
      enablePan={false}
      enableDamping={true}
      dampingFactor={0.05}
    />
  );
}