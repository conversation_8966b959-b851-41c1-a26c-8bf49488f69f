'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree, useFrame } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { useCameraSnapTracking } from '@/hooks/useCameraSnapTracking';
import * as THREE from 'three';

export default function ExperienceControls({ data }) {
  const { experienceState, experienceDispatch } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const refControls = useRef(null);
  const { camera, scene } = useThree();

  // Use custom hook for camera snap tracking with error handling
  const cameraTracking = useCameraSnapTracking();

  // Safely destructure with fallbacks
  const {
    isSnapping = () => false,
    hasObjectChanged = () => false,
    getTrackedObject = () => null,
    initializeSnap = () => false,
    updateSnapAnimation = () => false,
    clearTracking = () => {}
  } = cameraTracking || {};

  // Early return if essential dependencies are missing
  if (!camera || !scene) {
    console.warn("ExperienceControls: Missing camera or scene");
    return null;
  }

  /**
   * Smoothly snap camera to target object with configurable offset
   */
  const snapToTarget = useCallback((targetObject, customOffset) => {
    if (!refControls.current || !targetObject || !camera) {
      console.warn("snapToTarget: Missing required parameters");
      return;
    }

    try {
      // Default camera offset based on view mode
      let defaultOffset;
      if (experienceState?.firstPersonView) {
        defaultOffset = new THREE.Vector3(0, 0.2, 0.1); // Close to object for first-person
      } else {
        defaultOffset = new THREE.Vector3(0, 1, 2); // Behind and above for third-person
      }

      const offset = customOffset || defaultOffset;

      // Initialize the snap operation using the custom hook
      const success = initializeSnap(targetObject, camera, refControls.current, offset);

      if (success) {
        setControlsEnabled(false);
      }
    } catch (error) {
      console.warn("Error in snapToTarget:", error);
    }
  }, [camera, experienceState?.firstPersonView, initializeSnap]);

  /**
   * Handle activeRoomSnap changes and trigger camera snap
   */
  useEffect(() => {
    if (!scene) return;

    try {
      if (experienceState?.activeRoomSnap) {
        const foundObject = scene.getObjectByName(experienceState.activeRoomSnap);
        if (foundObject) {
          snapToTarget(foundObject);
        } else {
          console.warn(`Object with name "${experienceState.activeRoomSnap}" not found in the scene.`);
        }
      } else {
        // Clear tracking when no active snap
        clearTracking();
        if (isSnapping()) {
          setControlsEnabled(true);
        }
      }
    } catch (error) {
      console.warn("Error in activeRoomSnap effect:", error);
    }
  }, [scene, experienceState?.activeRoomSnap, snapToTarget, clearTracking, isSnapping]);

  /**
   * Main animation loop - handles smooth transitions and object tracking
   */
  useFrame((_, delta) => {
    const controls = refControls.current;
    if (!controls || !camera) return;

    try {
      // Handle snap animation using the custom hook
      if (isSnapping()) {
        const snapCompleted = updateSnapAnimation(delta, camera, controls);

        if (snapCompleted) {
          setControlsEnabled(true);
        }
      }
      // Track object changes when not actively snapping
      else {
        const trackedObject = getTrackedObject();
        if (trackedObject && hasObjectChanged && hasObjectChanged(trackedObject)) {
          console.log("Tracked object has moved/rotated, updating camera position");
          snapToTarget(trackedObject);
        }
      }
    } catch (error) {
      console.warn("Error in useFrame:", error);
    }
  });

  /**
   * Cycle through available room snap points
   */
  const cycleSnapPoints = useCallback(() => {
    if (!data?.roomSnaps || data.roomSnaps.length === 0) {
      console.warn("No room snaps available for cycling");
      return;
    }

    const snapNames = data.roomSnaps.map(snap => snap.name);
    const currentIndex = snapNames.findIndex(
      (snapName) => snapName === experienceState?.activeRoomSnap
    );

    const nextIndex = (currentIndex + 1) % snapNames.length;
    const nextSnapName = snapNames[nextIndex];

    experienceDispatch({ type: 'SET_ACTIVE_ROOM_SNAP', payload: nextSnapName });
  }, [data?.roomSnaps, experienceState?.activeRoomSnap, experienceDispatch]);

  /**
   * Manually snap to a specific object by name
   */
  const snapToObjectByName = useCallback((objectName, customOffset) => {
    if (!scene) {
      console.warn("Scene not available for object lookup");
      return;
    }

    try {
      const foundObject = scene.getObjectByName(objectName);
      if (foundObject) {
        snapToTarget(foundObject, customOffset);
      } else {
        console.warn(`Object with name "${objectName}" not found in the scene`);
      }
    } catch (error) {
      console.warn("Error in snapToObjectByName:", error);
    }
  }, [scene, snapToTarget]);

  /**
   * Reset camera to default position
   */
  const resetCamera = useCallback(() => {
    if (!camera || !refControls.current) return;

    try {
      clearTracking();
      setControlsEnabled(true);

      // Reset to default position
      camera.position.set(0, 5, 10);
      refControls.current.target.set(0, 0, 0);
      refControls.current.update();

      // Clear active room snap
      experienceDispatch({ type: 'SET_ACTIVE_ROOM_SNAP', payload: null });
    } catch (error) {
      console.warn("Error in resetCamera:", error);
    }
  }, [camera, clearTracking, experienceDispatch]);

  // Create a separate ref for exposing methods to avoid conflicts with OrbitControls ref
  const exposedMethodsRef = useRef();

  // Expose methods for external use
  React.useImperativeHandle(exposedMethodsRef, () => ({
    cycleSnapPoints,
    snapToObjectByName,
    resetCamera,
    isSnapping,
    getTrackedObject
  }), [cycleSnapPoints, snapToObjectByName, resetCamera, isSnapping, getTrackedObject]);

  // Safe parsing of distance values
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 100);

  return (
    <OrbitControls
      ref={refControls}
      minDistance={minDistance}
      maxDistance={maxDistance}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
      rotateSpeed={-0.25}
      enabled={controlsEnabled}
      enablePan={false}
      enableDamping={true}
      dampingFactor={0.05}
    />
  );
}