'use client'

import React, { Suspense } from 'react'
import ExperienceUi from './ExperienceUi'
// import ExperienceWorld from './ExperienceWorld'
import dynamic from 'next/dynamic'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useLoader } from '@react-three/fiber'
import * as THREE from 'three' // Import THREE for useLoader

const ExperienceWorld = dynamic(() => import('./ExperienceWorld'),{ssr:false})

// Dynamically import Preloader to ensure it only runs on the client
const Preloader = dynamic(
  () => import('./Preloader'), // Create a new file for Preloader
  { ssr: false }
);


export default function ExperienceWrapper({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()

  // Determine the URL for the default 360 image
  // Make sure data and _360sImages are available here to get the URL
  const default360ImageUrl = data?._360sImages?.[0]?.url;
  
  // console.log('ExperienceWrapper:',data)
  return (
    <div className='flex relative w-full h-full'>
      <ExperienceUi data={data}/>
      <Suspense fallback={null}>
        {/* Preload the default texture here */}
        {default360ImageUrl  && <Preloader initial360ImageUrl={default360ImageUrl} />}
        {/* Render the ExperienceWorld component */}
        <ExperienceWorld data={data}/>
      </Suspense>
    </div>
  )
}
