'use client'
import React, { useRef, useState, useEffect } from 'react'
import { BackSide } from 'three'
import * as THREE from 'three'
import { useThree, useLoader } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { degToRad } from 'three/src/math/MathUtils'

export default function Experience360({data}) {
  // console.log('ExperienceModel:',data?._360sImages)
  const {experienceState,experienceDispatch}=useExperienceContext()
  // const [textureLoaded,setTextureLoaded]=useState(false)
  // const [texture,setTexture]=useState(null)
  // const [textureArray,setTextureArray]=useState([])
  // const refLoaderManager=useRef(new THREE.LoadingManager())
  // const refLoader=useRef(new THREE.TextureLoader(refLoaderManager.current))
  const refSphere=useRef(null)
  const {scene}=useThree()

  const currentTextureUrl = data?._360sImages?.[experienceState?.textureIndex || 0]?.url;
  const texture = useLoader(THREE.TextureLoader, currentTextureUrl)
  
 useEffect(() => {
    if (texture && currentTextureUrl) {
      console.log('Experience360: New texture displayed for URL:', currentTextureUrl);
      // You could dispatch an event here if needed, e.g., to signal that the scene is ready.
      // experienceDispatch({ type: 'TEXTURE_READY', payload: currentTextureUrl });
    }
  }, [texture, currentTextureUrl])
  
  // console.log('Experience360:',texture)
  
  return (
    <>
      {texture && currentTextureUrl && <mesh 
        ref={refSphere}
        name="Experience360"
        scale={[1,1,-1]}
        rotation-y={degToRad(90)}
      >
        <meshBasicMaterial 
          map={texture}
          side={BackSide}
        />
        <sphereGeometry args={[128,128,128]}/>
      </mesh>}
    </>
  )
}
