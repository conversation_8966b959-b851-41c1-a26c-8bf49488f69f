'use client'
import React, {useEffect, useCallback} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceModel({data}) {
  const {experienceState}=useExperienceContext() // experienceDispatch is not used here
  const {scene}=useThree() 

  /**
   * Smart Level Visibility Management
   * Implements priority-based visibility control for hideLevel objects
   */
  const handleLevelToHde = useCallback(() => {
    if (!experienceState?.levelToHide || !data?.hideLevel || !scene) {
      console.warn('handleLevelToHde: Missing required parameters');
      return;
    }

    try {
      const hideGroup = scene.getObjectByName('hideLevel');
      if (!hideGroup) {
        console.warn('handleLevelToHde: hideLevel group not found in scene');
        return;
      }

      // Get the target object from experienceState
      const targetLevelName = experienceState.levelToHide?.name;
      if (!targetLevelName) {
        console.warn('handleLevelToHde: No target level name provided');
        return;
      }

      // Find target level in data.hideLevel array to get its priority
      const targetLevelIndex = data.hideLevel.findIndex(level => level.name === targetLevelName);
      if (targetLevelIndex === -1) {
        console.warn(`handleLevelToHde: Target level "${targetLevelName}" not found in hideLevel array`);
        return;
      }

      const targetPriority = targetLevelIndex; // Array index = priority (lower index = higher priority)

      // Analyze current visibility state of all hideLevel objects
      const levelStates = data.hideLevel.map((level, index) => {
        const levelObject = hideGroup.getObjectByName(level.name);
        if (!levelObject) {
          return { ...level, priority: index, isVisible: false, object: null };
        }

        let isVisible = false;
        levelObject.traverse(child => {
          if (child.isMesh && child.visible) {
            isVisible = true;
          }
        });

        return {
          ...level,
          priority: index,
          isVisible,
          object: levelObject
        };
      });

      const visibleLevels = levelStates.filter(level => level.isVisible);
      const hiddenLevels = levelStates.filter(level => !level.isVisible);
      const targetLevel = levelStates.find(level => level.name === targetLevelName);

      if (!targetLevel || !targetLevel.object) {
        console.warn(`handleLevelToHde: Target level object "${targetLevelName}" not found`);
        return;
      }

      console.log('Level visibility analysis:', {
        targetLevel: targetLevel.name,
        targetPriority,
        isCurrentlyVisible: targetLevel.isVisible,
        visibleLevels: visibleLevels.map(l => ({ name: l.name, priority: l.priority })),
        hiddenLevels: hiddenLevels.map(l => ({ name: l.name, priority: l.priority }))
      });

      // Apply priority-based visibility logic
      let shouldToggle = false;

      if (targetLevel.isVisible) {
        // Target is visible - hide it only if it has the highest priority among visible objects
        const highestPriorityVisible = Math.min(...visibleLevels.map(l => l.priority));
        shouldToggle = targetPriority === highestPriorityVisible;

        if (shouldToggle) {
          console.log(`Hiding level "${targetLevelName}" (highest priority among visible: ${targetPriority})`);
        } else {
          console.log(`Cannot hide level "${targetLevelName}" - not highest priority (${targetPriority}), highest is ${highestPriorityVisible}`);
        }
      } else {
        // Target is hidden - show it only if it has the lowest priority among hidden objects
        const lowestPriorityHidden = Math.max(...hiddenLevels.map(l => l.priority));
        shouldToggle = targetPriority === lowestPriorityHidden;

        if (shouldToggle) {
          console.log(`Showing level "${targetLevelName}" (lowest priority among hidden: ${targetPriority})`);
        } else {
          console.log(`Cannot show level "${targetLevelName}" - not lowest priority (${targetPriority}), lowest is ${lowestPriorityHidden}`);
        }
      }

      // Toggle visibility if conditions are met
      if (shouldToggle) {
        targetLevel.object.traverse((child) => {
          if (child.isMesh) {
            child.visible = !child.visible;
          }
        });
        console.log(`Successfully toggled visibility for level "${targetLevelName}"`);
      }

    } catch (error) {
      console.error('Error in handleLevelToHde:', error);
    }
  }, [experienceState?.levelToHide, data?.hideLevel, scene]);

  useEffect(() => {
    handleLevelToHde()
  }, [handleLevelToHde])

  // console.log('ExperienceModel:',data)
  
  return (
    <>
      <group 
        name="ExperienceModel"
        position={data?.position?.split(',').map(i=>Number(i))}
      >
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
         <group name="hideLevel">
          {data?.hideLevel?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
